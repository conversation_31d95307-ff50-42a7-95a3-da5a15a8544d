import bettercam
import torch
import time
import math
import keyboard
import threading
import socket
import win32api
from ultralytics import YOLO
import pandas as pd
import json
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import secrets

# Encryption configuration
DEFAULT_ENCRYPTION_KEY = "SilenceCapture2024KeyForSecureUDP!!"
ENABLE_ENCRYPTION = True

# Encryption utilities
class CryptoUtils:
    @staticmethod
    def pad_data(data):
        """Add custom padding to match Rust implementation"""
        padding_needed = 16 - (len(data) % 16)
        if padding_needed != 16:
            data.extend([padding_needed] * padding_needed)
        return data
    
    @staticmethod
    def encrypt(data_str, key_str):
        """Encrypt data using AES-256-ECB to match Rust implementation"""
        try:
            # Create 256-bit key from provided key string (match Rust behavior)
            key_bytes = key_str.encode('utf-8')[:32].ljust(32, b'\0')
            
            # Convert string to bytes and pad
            data_bytes = bytearray(data_str.encode('utf-8'))
            padded_data = CryptoUtils.pad_data(data_bytes)
            
            # Prepare cipher (ECB mode like Rust)
            cipher = Cipher(algorithms.AES(key_bytes), modes.ECB(), backend=default_backend())
            encryptor = cipher.encryptor()
            
            # Encrypt the padded data
            encrypted_data = encryptor.update(bytes(padded_data)) + encryptor.finalize()
            
            # Generate random IV (16 bytes) like Rust does
            iv = secrets.token_bytes(16)
            
            # Prepend IV to encrypted data (match Rust behavior)
            result = iv + encrypted_data
            
            # Encode as base64
            return base64.b64encode(result).decode('utf-8')
        except Exception as e:
            print(f"Encryption error: {e}")
            return None

def send_encrypted_message(message, udp_socket, target_ip, target_port):
    """Send encrypted UDP message to action.rs"""
    try:
        json_message = json.dumps(message)
        
        if ENABLE_ENCRYPTION:
            encrypted_data = CryptoUtils.encrypt(json_message, DEFAULT_ENCRYPTION_KEY)
            if encrypted_data:
                udp_socket.sendto(encrypted_data.encode(), (target_ip, target_port))
            else:
                print("Failed to encrypt message, sending unencrypted")
                udp_socket.sendto(json_message.encode(), (target_ip, target_port))
        else:
            udp_socket.sendto(json_message.encode(), (target_ip, target_port))
    except Exception as e:
        print(f"Failed to send UDP message: {e}")


def cooldown(cooldown_bool, wait):
    time.sleep(wait)
    cooldown_bool[0] = True



# Configuration
SENS = 0.30
AIM_SPEED = 1*(1/SENS)
target_multiply = [0,1.01,1.025,1.05,1.05,1.05,1.05,1.05,1.05,1.05,1.05]
COORDINATE_SCALE = 1.5  # Adjust this value to fine-tune accuracy

# Network configuration for sending coordinates to action.rs
UDP_TARGET_IP = "127.0.0.1"  # localhost
UDP_TARGET_PORT = 12345  # action.rs listening port (should match action_config.toml)
udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
activation_range = 300


# Monitor and region setup
MONITOR_WIDTH = 1920  # game res
MONITOR_HEIGHT = 1080  # game res
MONITOR_SCALE = 5  # how much the screen shot is downsized by eg. 5 would be one fifth of the monitor dimensions
region = (int(MONITOR_WIDTH/2-MONITOR_WIDTH/MONITOR_SCALE/2),int(MONITOR_HEIGHT/2-MONITOR_HEIGHT/MONITOR_SCALE/2),int(MONITOR_WIDTH/2+MONITOR_WIDTH/MONITOR_SCALE/2),int(MONITOR_HEIGHT/2+MONITOR_HEIGHT/MONITOR_SCALE/2))
x,y,width,height = region
screenshot_center = [int((width-x)/2),int((height-y)/2)]

# Silent aim variables
silent_aim = False
silent_toggle = [True]
no_fov_cooldown = [True]

# Initialize PyTorch and model
torch.cuda.set_device(0)
model = YOLO(r"G:\silence-ai\custom_best.engine", task='detect')
camera = bettercam.create(output_idx=0, output_color="RGB")

print("Silent aim system initialized. Press F8 to toggle silent aim, Up/Down arrows to adjust FOV.")
print(f"Initial FOV: {activation_range}")
print("Silent aim: Inactive")






while True:
    closest_part_distance = 100000
    closest_part = -1
    screenshot = camera.grab(region)
    if screenshot is None: continue
    results = model.predict(screenshot, imgsz=736, classes=1, conf=0.4, max_det=300, device="cuda:0" , verbose=False)
    data = []
    for box in results[0].boxes.xyxy:
        # Extract bounding box coordinates
        xmin, ymin, xmax, ymax = box[0], box[1], box[2], box[3]

        # Initialize confidence score and class label to None
        confidence = None
        class_label = None

        # Check if box has sufficient elements
        if len(box) >= 5:
            confidence = box[4]  # Extract confidence score
        if len(box) >= 6:
            class_label = box[5]  # Extract class label

        # Create a dictionary containing the extracted information
        box_data = {
            'xmin': xmin,
            'ymin': ymin,
            'xmax': xmax,
            'ymax': ymax,
            'confidence': confidence,
            'class': class_label
        }

        # Append the dictionary to the data list
        data.append(box_data)
    
    df = pd.DataFrame(data)

    # Find closest target
    for i in range(0,10):
        try:
            xmin = int(df.iloc[i,0])
            ymin = int(df.iloc[i,1])
            xmax = int(df.iloc[i,2])
            ymax = int(df.iloc[i,3])

            centerX = (xmax-xmin)/2+xmin 
            centerY = (ymax-ymin)/2+ymin

            distance = math.dist([centerX,centerY],screenshot_center)

            if int(distance) < closest_part_distance:
                closest_part_distance = distance
                closest_part = i
        except:
            continue

    # Handle keyboard input for silent aim toggle
    if keyboard.is_pressed('f8'):
        if silent_toggle[0] == True:
            silent_aim = not silent_aim
            if silent_aim:
                print("Silent Aim: Active")
            else:
                print("Silent Aim: Inactive")
            silent_toggle[0] = False
            thread = threading.Thread(target=cooldown, args=(silent_toggle,0.2,))
            thread.start()

    # Handle FOV adjustment
    if keyboard.is_pressed('up') and no_fov_cooldown[0] == True:
        activation_range += 5
        print(f"FOV: {activation_range}")
        no_fov_cooldown[0] = False
        thread = threading.Thread(target=cooldown, args=(no_fov_cooldown,0.05,))
        thread.start()

    if keyboard.is_pressed('down') and no_fov_cooldown[0] == True:
        activation_range -= 5
        print(f"FOV: {activation_range}")
        no_fov_cooldown[0] = False
        thread = threading.Thread(target=cooldown, args=(no_fov_cooldown,0.05,))
        thread.start()

    if closest_part != -1:
        xmin = df.iloc[closest_part,0]
        ymin = df.iloc[closest_part,1]
        xmax = df.iloc[closest_part,2]
        ymax = df.iloc[closest_part,3]

        head_center_list = [(xmax-xmin)/2+xmin,(ymax-ymin)/2+ymin]
        
        # Check distance to center for activation
        distance_to_center = math.dist(head_center_list, screenshot_center)
        
        if distance_to_center <= activation_range:
            xdif = (head_center_list[0]-screenshot_center[0])*AIM_SPEED*target_multiply[MONITOR_SCALE]*COORDINATE_SCALE
            ydif = (head_center_list[1]-screenshot_center[1])*AIM_SPEED*target_multiply[MONITOR_SCALE]*COORDINATE_SCALE
            if silent_aim == True and win32api.GetAsyncKeyState(0x06)&0x8000 > 0:
                message = {
                    "center_x": float(xdif),
                    "center_y": float(ydif),
                    "message_type": "silent_aim",
                    "timestamp": int(time.time() * 1000)
                }
                send_encrypted_message(message, udp_socket, UDP_TARGET_IP, UDP_TARGET_PORT)
                time.sleep(0.2)  # Add sleep timer to prevent spam

