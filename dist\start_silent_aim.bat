@echo off
echo ===============================================
echo     Silence Network - Silent Aim System
echo ===============================================
echo Starting Python Silent Aim script...
echo.
echo Requirements:
echo - Python with required packages installed
echo - YOLO model file at G:\silence-ai\custom_best.engine
echo - action.exe must be running for mouse control
echo.
echo Controls:
echo - F8: Toggle silent aim on/off
echo - Up/Down arrows: Adjust FOV range
echo - Right mouse button: Activate silent aim (when enabled)
echo.
python main_ai.py
echo.
echo Script ended. Press any key to close...
pause > nul
