# Silence Network - Distribution Package

This folder contains the complete Silence Network system for silent aim functionality.

## Files Included:

### Executables (Release Builds):
- `action.exe` - Mouse control and Arduino communication (Windows GUI, 4.35 MB)
- `capture.exe` - Screen capture and target detection (Windows GUI, 4.34 MB)

### Python Scripts:
- `main_ai.py` - Silent aim AI script with YOLO detection (8.4 KB)

### Configuration Files:
- `action_config.toml` - Action application settings
- `capture_config.toml` - Capture application settings

### Launchers:
- `start_action.bat` - Launch action GUI
- `start_capture.bat` - Launch capture GUI  
- `start_silent_aim.bat` - Launch Python silent aim script

## Quick Start:

1. **For Silent Aim System:**
   - Run `start_action.bat` (handles mouse control)
   - Run `start_silent_aim.bat` (AI detection and control)

2. **For Screen Capture:**
   - Run `start_capture.bat` (screen capture utility)

## Controls:
- **F8**: Toggle silent aim on/off
- **Up/Down arrows**: Adjust FOV range  
- **Right mouse button**: Activate silent aim (when enabled)

## Requirements:
- Python with required packages (torch, ultralytics, bettercam, etc.)
- YOLO model file at `G:\silence-ai\custom_best.engine`
- Arduino hardware (optional, for physical mouse control)

## Notes:
- All executables are Windows GUI applications (no console windows)
- Communication between components uses encrypted UDP
- Silent aim is controlled entirely by the Python script
