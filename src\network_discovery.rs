use std::net::{IpAddr, Ipv4Addr, UdpSocket, SocketAddr};
use std::time::{Duration, Instant};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::thread;
use serde::{Deserialize, Serialize};
use if_addrs::get_if_addrs;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct NetworkDevice {
    pub ip: String,
    pub name: String,
    pub device_type: DeviceType,
    pub status: ConnectionStatus,
    pub last_seen: Instant,
    pub response_time: Option<Duration>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeviceType {
    CaptureDevice,
    ActionDevice,
    Unknown,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionStatus {
    Connected,
    Disconnected,
    Scanning,
    Error(String),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct DiscoveryMessage {
    device_type: String,
    device_name: String,
    version: String,
    capabilities: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct DiscoveryResponse {
    device_type: String,
    device_name: String,
    version: String,
    status: String,
}

pub struct NetworkDiscovery {
    devices: Arc<Mutex<HashMap<String, NetworkDevice>>>,
    discovery_socket: Option<UdpSocket>,
    local_ip: Option<IpAddr>,
    scan_range: Vec<Ipv4Addr>,
    is_scanning: Arc<Mutex<bool>>,
    discovery_port: u16,
}

impl NetworkDiscovery {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let local_ip = Self::get_local_ip();
        let scan_range = Self::calculate_scan_range(&local_ip)?;
        
        Ok(Self {
            devices: Arc::new(Mutex::new(HashMap::new())),
            discovery_socket: None,
            local_ip,
            scan_range,
            is_scanning: Arc::new(Mutex::new(false)),
            discovery_port: 5003, // Discovery port
        })
    }

    fn get_local_ip() -> Option<IpAddr> {
        if let Ok(if_addrs) = get_if_addrs() {
            for iface in if_addrs {
                if !iface.is_loopback() && iface.ip().is_ipv4() {
                    return Some(iface.ip());
                }
            }
        }
        None
    }

    fn calculate_scan_range(local_ip: &Option<IpAddr>) -> Result<Vec<Ipv4Addr>, Box<dyn std::error::Error>> {
        let mut range = Vec::new();
        
        if let Some(IpAddr::V4(ip)) = local_ip {
            let ip_bytes = ip.octets();
            let network_base = format!("{}.{}.{}", ip_bytes[0], ip_bytes[1], ip_bytes[2]);
            
            // Scan the local subnet (e.g., ***********-254)
            for i in 1..=254 {
                let target_ip = format!("{}.{}", network_base, i);
                if let Ok(addr) = target_ip.parse::<Ipv4Addr>() {
                    range.push(addr);
                }
            }
        } else {
            // Fallback to common private networks
            for network in &["192.168.1", "192.168.0", "10.0.0", "172.16.0"] {
                for i in 1..=254 {
                    let target_ip = format!("{}.{}", network, i);
                    if let Ok(addr) = target_ip.parse::<Ipv4Addr>() {
                        range.push(addr);
                    }
                }
            }
        }
        
        Ok(range)
    }

    pub fn start_discovery(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Set up discovery socket
        let socket = UdpSocket::bind("0.0.0.0:0")?;
        socket.set_broadcast(true)?;
        socket.set_nonblocking(true)?;
        self.discovery_socket = Some(socket);

        // Start scanning thread
        let devices = Arc::clone(&self.devices);
        let is_scanning = Arc::clone(&self.is_scanning);
        let scan_range = self.scan_range.clone();
        let discovery_port = self.discovery_port;

        *is_scanning.lock().unwrap() = true;

        thread::spawn(move || {
            Self::discovery_thread(devices, is_scanning, scan_range, discovery_port);
        });

        Ok(())
    }

    fn discovery_thread(
        devices: Arc<Mutex<HashMap<String, NetworkDevice>>>,
        is_scanning: Arc<Mutex<bool>>,
        scan_range: Vec<Ipv4Addr>,
        discovery_port: u16,
    ) {
        while *is_scanning.lock().unwrap() {
            // Send discovery broadcasts
            if let Ok(socket) = UdpSocket::bind("0.0.0.0:0") {
                socket.set_broadcast(true).ok();
                socket.set_nonblocking(true).ok();
                
                let discovery_msg = DiscoveryMessage {
                    device_type: "scanner".to_string(),
                    device_name: "Network Scanner".to_string(),
                    version: "1.0".to_string(),
                    capabilities: vec!["discovery".to_string()],
                };

                if let Ok(msg_data) = serde_json::to_vec(&discovery_msg) {
                    // Broadcast to local network
                    for ip in &scan_range {
                        let addr = SocketAddr::new(IpAddr::V4(*ip), discovery_port);
                        socket.send_to(&msg_data, addr).ok();
                        
                        // Also try common ports for our applications
                        for port in &[5004, 5005, 12346] {
                            let addr = SocketAddr::new(IpAddr::V4(*ip), *port);
                            socket.send_to(&msg_data, addr).ok();
                        }
                    }
                }

                // Listen for responses
                let mut buffer = [0u8; 1024];
                let timeout = Instant::now() + Duration::from_millis(2000);
                
                while Instant::now() < timeout {
                    if let Ok((size, addr)) = socket.recv_from(&mut buffer) {
                        if let Ok(response) = serde_json::from_slice::<DiscoveryResponse>(&buffer[..size]) {
                            let device = NetworkDevice {
                                ip: addr.ip().to_string(),
                                name: response.device_name,
                                device_type: match response.device_type.as_str() {
                                    "capture" => DeviceType::CaptureDevice,
                                    "action" => DeviceType::ActionDevice,
                                    _ => DeviceType::Unknown,
                                },
                                status: if response.status == "active" {
                                    ConnectionStatus::Connected
                                } else {
                                    ConnectionStatus::Disconnected
                                },
                                last_seen: Instant::now(),
                                response_time: Some(Duration::from_millis(100)), // Approximate
                            };

                            devices.lock().unwrap().insert(addr.ip().to_string(), device);
                        }
                    }
                    thread::sleep(Duration::from_millis(10));
                }
            }            // Also try ping-based discovery for common ports
            Self::ping_scan(Arc::clone(&devices), &scan_range);

            thread::sleep(Duration::from_secs(5)); // Scan every 5 seconds
        }
    }    fn ping_scan(devices: Arc<Mutex<HashMap<String, NetworkDevice>>>, scan_range: &[Ipv4Addr]) {
        for ip in scan_range.iter().take(50) { // Limit to prevent network flooding
            let ip_str = ip.to_string();
            
            // Try to connect to common ports to detect devices
            for (port, device_type) in &[(5004, DeviceType::CaptureDevice), (12346, DeviceType::ActionDevice)] {
                if let Ok(socket) = UdpSocket::bind("0.0.0.0:0") {
                    socket.set_nonblocking(true).ok();
                    socket.set_read_timeout(Some(Duration::from_millis(100))).ok();
                    
                    let addr = SocketAddr::new(IpAddr::V4(*ip), *port);
                    let test_data = b"ping";
                    
                    if socket.send_to(test_data, addr).is_ok() {
                        // If we can send, assume device might be there
                        let mut devices_lock = devices.lock().unwrap();
                        let ip_string = ip_str.clone();
                        if !devices_lock.contains_key(&ip_string) {
                            let device = NetworkDevice {
                                ip: ip_string.clone(),
                                name: format!("{:?} Device", device_type),
                                device_type: device_type.clone(),
                                status: ConnectionStatus::Disconnected, // Will be updated if responds
                                last_seen: Instant::now(),
                                response_time: None,
                            };
                            devices_lock.insert(ip_string, device);
                        }
                    }
                }
            }
        }
    }

    pub fn get_devices(&self) -> Vec<NetworkDevice> {
        let devices = self.devices.lock().unwrap();
        let mut device_list: Vec<NetworkDevice> = devices.values().cloned().collect();
        
        // Remove devices not seen in the last 30 seconds
        let now = Instant::now();
        device_list.retain(|device| now.duration_since(device.last_seen) < Duration::from_secs(30));
        
        // Sort by IP address
        device_list.sort_by(|a, b| {
            a.ip.parse::<Ipv4Addr>().unwrap_or(Ipv4Addr::new(0, 0, 0, 0))
                .cmp(&b.ip.parse::<Ipv4Addr>().unwrap_or(Ipv4Addr::new(0, 0, 0, 0)))
        });
        
        device_list
    }

    pub fn connect_to_device(&self, device_ip: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut devices = self.devices.lock().unwrap();
        if let Some(device) = devices.get_mut(device_ip) {
            device.status = ConnectionStatus::Connected;
            device.last_seen = Instant::now();
        }
        Ok(())
    }

    pub fn disconnect_from_device(&self, device_ip: &str) -> Result<(), Box<dyn std::error::Error>> {
        let mut devices = self.devices.lock().unwrap();
        if let Some(device) = devices.get_mut(device_ip) {
            device.status = ConnectionStatus::Disconnected;
        }
        Ok(())
    }

    pub fn stop_discovery(&self) {
        *self.is_scanning.lock().unwrap() = false;
    }

    pub fn is_scanning(&self) -> bool {
        *self.is_scanning.lock().unwrap()
    }    pub fn get_local_ip_string(&self) -> Option<String> {
        self.local_ip.map(|ip| ip.to_string())
    }
}

impl Drop for NetworkDiscovery {
    fn drop(&mut self) {
        self.stop_discovery();
    }
}
