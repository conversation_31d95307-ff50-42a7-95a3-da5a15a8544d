use eframe::egui;
use egui::TextureHandle;
use parking_lot::Mutex;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::thread;
use std::time::{Duration, Instant};
use windows::core::*;
use windows::Win32::Foundation::*;
use windows::Win32::Graphics::Direct3D::*;
use windows::Win32::Graphics::Direct3D11::*;
use windows::Win32::Graphics::Dxgi::*;
use windows::Win32::Graphics::Dxgi::Common::*;
use windows::Win32::System::Threading::*;
use windows::Win32::UI::WindowsAndMessaging::*;
use windows::Win32::UI::Input::KeyboardAndMouse::*;
use serialport::{SerialPort, available_ports};
use std::io::Write;
use serde::{Deserialize, Serialize};
use anyhow::{Result, Context};
use rayon::prelude::*;

// Color presets based on capture.rs
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ColorPreset {
    Yellow,        // [30, 125, 150] - [30, 255, 255] in OpenCV HSV
    Yellow2,       // [30, 170, 254] - [30, 230, 255] in OpenCV HSV
    Purple,        // [144, 72, 150] - [152, 255, 255] in OpenCV HSV
    AntiAstra,     // [140, 86, 172] - [150, 255, 255] in OpenCV HSV
    Red,           // [0, 170, 150] - [5, 255, 255] in OpenCV HSV
    Custom,
}

impl Default for ColorPreset {
    fn default() -> Self {
        ColorPreset::Purple
    }
}

impl ColorPreset {
    pub fn to_hsv_range(&self) -> ([f32; 3], [f32; 3]) {
        match self {
            ColorPreset::Yellow => (
                [0.15000000596046448, 0.41499999165534973, 0.7300000190734863],
                [0.17000000178813934, 1.0, 1.0]
            ),
            ColorPreset::Yellow2 => (
                Self::opencv_to_rust_hsv([30.0, 170.0, 254.0]),
                Self::opencv_to_rust_hsv([30.0, 230.0, 255.0])
            ),
            ColorPreset::Purple => (
                Self::opencv_to_rust_hsv([144.0, 72.0, 150.0]),
                Self::opencv_to_rust_hsv([152.0, 255.0, 255.0])
            ),
            ColorPreset::AntiAstra => (
                Self::opencv_to_rust_hsv([140.0, 86.0, 172.0]),
                Self::opencv_to_rust_hsv([150.0, 255.0, 255.0])
            ),
            ColorPreset::Red => (
                Self::opencv_to_rust_hsv([0.0, 170.0, 150.0]),
                Self::opencv_to_rust_hsv([5.0, 255.0, 255.0])
            ),
            ColorPreset::Custom => (
                [0.0, 0.0, 0.0],
                [1.0, 1.0, 1.0]
            ),
        }
    }
    
    // Convert OpenCV HSV (H: 0-179, S: 0-255, V: 0-255) to Rust HSV (H: 0-1, S: 0-1, V: 0-1)
    fn opencv_to_rust_hsv(opencv_hsv: [f32; 3]) -> [f32; 3] {
        [
            opencv_hsv[0] / 179.0,  // H: 0-179 -> 0-1
            opencv_hsv[1] / 255.0,  // S: 0-255 -> 0-1
            opencv_hsv[2] / 255.0,  // V: 0-255 -> 0-1
        ]
    }
    
    pub fn name(&self) -> &'static str {
        match self {
            ColorPreset::Yellow => "Yellow",
            ColorPreset::Yellow2 => "Yellow 2",
            ColorPreset::Purple => "Purple",
            ColorPreset::AntiAstra => "Anti Astra",
            ColorPreset::Red => "Red",
            ColorPreset::Custom => "Custom",
        }
    }
}

// Configuration structures for TOML parsing
#[derive(Debug, Clone, Serialize, Deserialize)]
struct TomlConfig {
    pub settings: TomlSettings,
    pub aim: AimConfig,
    pub trigger: TriggerConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct TomlSettings {
    pub com_port: String,
    pub aim_assist: bool,
    pub trigger_bot: bool,
    pub aim_key: String,
    pub trigger_key: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AimConfig {
    pub aim_fov: u32,
    pub aim_speed_x: f32,
    pub aim_speed_y: f32,
    pub aim_offset: i32,
    pub aim_offset_x: i32,
    pub aim_fps: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct TriggerConfig {
    pub trigger_fov: u32,
    pub trigger_delay: f32,
    pub trigger_fps: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AppConfig {
    pub com_port: String,
    pub aim_assist: bool,
    pub trigger_bot: bool,
    pub aim_key: String,
    pub trigger_key: u32,
    pub color_preset: ColorPreset,
    pub custom_hsv_lower: [f32; 3],
    pub custom_hsv_upper: [f32; 3],
    pub aim: AimConfig,
    pub trigger: TriggerConfig,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            com_port: "auto".to_string(),
            aim_assist: false,
            trigger_bot: false,
            aim_key: "auto".to_string(),
            trigger_key: 0x05, // Right mouse button
            color_preset: ColorPreset::Purple,
            custom_hsv_lower: [0.8, 0.282, 0.588],
            custom_hsv_upper: [0.844, 1.0, 1.0],
            aim: AimConfig {
                aim_fov: 65,
                aim_speed_x: 0.6,
                aim_speed_y: 0.3,
                aim_offset: 8,
                aim_offset_x: 0,
                aim_fps: 285,
            },
            trigger: TriggerConfig {
                trigger_fov: 8,
                trigger_delay: 0.2,
                trigger_fps: 285,
            },
        }
    }
}

impl AppConfig {
    fn load_from_file() -> Self {
        match std::fs::read_to_string("config.toml") {
            Ok(content) => {
                match toml::from_str::<TomlConfig>(&content) {
                    Ok(toml_config) => {
                        Self {
                            com_port: toml_config.settings.com_port,
                            aim_assist: toml_config.settings.aim_assist,
                            trigger_bot: toml_config.settings.trigger_bot,
                            aim_key: toml_config.settings.aim_key,
                            trigger_key: toml_config.settings.trigger_key,
                            color_preset: ColorPreset::Purple,
                            custom_hsv_lower: [0.8, 0.282, 0.588],
                            custom_hsv_upper: [0.844, 1.0, 1.0],
                            aim: toml_config.aim,
                            trigger: toml_config.trigger,
                        }
                    }
                    Err(_) => Self::default()
                }
            }
            Err(_) => Self::default()
        }
    }
}

// Arduino communication
struct ArduinoController {
    port: Box<dyn SerialPort>,
}

impl ArduinoController {
    fn new() -> Result<Self> {
        let port_name = Self::detect_arduino_port()?;
        let port = serialport::new(&port_name, 115200)
            .timeout(Duration::from_millis(100))
            .open()
            .context("Failed to open Arduino port")?;
        Ok(Self { port })
    }
    
    fn detect_arduino_port() -> Result<String> {
        let ports = available_ports().context("Failed to list serial ports")?;
        
        for port in ports {
            let port_name_lower = port.port_name.to_lowercase();
            let has_arduino_name = port_name_lower.contains("arduino") || port_name_lower.contains("com");
            
            let is_usb_serial = match &port.port_type {
                serialport::SerialPortType::UsbPort(usb_info) => {
                    let desc = format!("{:?}", usb_info).to_lowercase();
                    desc.contains("arduino") || desc.contains("usb")
                }
                _ => false,
            };
            
            if has_arduino_name || is_usb_serial {
                return Ok(port.port_name);
            }
        }
        
        anyhow::bail!("No Arduino found")
    }
    
    fn mousemove_aim(&mut self, x: i32, y: i32, message: &str) -> Result<()> {
        let mut x_coord = if x < 0 { x + 256 } else { x };
        let mut y_coord = if y < 0 { y + 256 } else { y };
        
        x_coord = x_coord.max(0).min(255);
        y_coord = y_coord.max(0).min(255);
        
        let coord_bytes = [x_coord as u8, y_coord as u8];
        let message_bytes = format!("{}\n", message).into_bytes();
        
        self.port.write_all(&coord_bytes)?;
        self.port.write_all(&message_bytes)?;
        
        Ok(())
    }
    
    fn mouse_click(&mut self) -> Result<()> {
        self.mousemove_aim(0, 0, "mouseclick")
    }
}

// Target detection result
#[derive(Debug, Clone)]
struct TargetInfo {
    center_x: f32,
    center_y: f32,
    #[allow(dead_code)]
    confidence: f32,
}

// Aim assist logic
struct AimAssist {
    config: Arc<Mutex<AppConfig>>,
    arduino: Option<ArduinoController>,
    last_aim_time: Instant,
    last_trigger_time: Instant,
}

#[derive(Clone)]
struct FrameData {
    pixels: Vec<egui::Color32>,
    filtered_pixels: Vec<egui::Color32>,
    fps: f32,
}

// Convert RGB to HSV (optimized)
fn rgb_to_hsv(r: f32, g: f32, b: f32) -> [f32; 3] {
    let max = r.max(g.max(b));
    let min = r.min(g.min(b));
    let delta = max - min;
    
    let v = max;
    let s = if max == 0.0 { 0.0 } else { delta / max };
    
    let h = if delta == 0.0 {
        0.0
    } else if max == r {
        ((g - b) / delta) % 6.0
    } else if max == g {
        (b - r) / delta + 2.0
    } else {
        (r - g) / delta + 4.0
    };
    
    [h / 6.0, s, v]
}

// Check if HSV values are within the target range (configurable)
fn is_target_color(hsv: [f32; 3], hsv_lower: [f32; 3], hsv_upper: [f32; 3]) -> bool {
    let [h, s, v] = hsv;
    
    if s < 0.1 || v < 0.1 {
        return false;
    }
    
    h >= hsv_lower[0] && h <= hsv_upper[0] &&
    s >= hsv_lower[1] && s <= hsv_upper[1] &&
    v >= hsv_lower[2] && v <= hsv_upper[2]
}

// Apply color filtering using parallel processing (configurable)
fn apply_color_filter(pixels: &[egui::Color32], hsv_lower: [f32; 3], hsv_upper: [f32; 3]) -> Vec<egui::Color32> {
    pixels.par_iter().map(|&pixel| {
        let r = pixel.r() as f32 / 255.0;
        let g = pixel.g() as f32 / 255.0;
        let b = pixel.b() as f32 / 255.0;
        
        let hsv = rgb_to_hsv(r, g, b);
        
        if is_target_color(hsv, hsv_lower, hsv_upper) {
            pixel
        } else {
            egui::Color32::from_rgba_premultiplied(
                (pixel.r() as f32 * 0.1) as u8,
                (pixel.g() as f32 * 0.1) as u8,
                (pixel.b() as f32 * 0.1) as u8,
                pixel.a(),
            )
        }
    }).collect()
}

struct DirectXCapture {
    context: ID3D11DeviceContext,
    duplication: IDXGIOutputDuplication,
    staging_texture: ID3D11Texture2D,
    center_x: u32,
    center_y: u32,
    capture_size: u32,
}

impl DirectXCapture {
    fn new(capture_size: u32) -> windows::core::Result<Self> {
        unsafe {
            let screen_width = GetSystemMetrics(SM_CXSCREEN) as u32;
            let screen_height = GetSystemMetrics(SM_CYSCREEN) as u32;
            let center_x = (screen_width / 2) - (capture_size / 2);
            let center_y = (screen_height / 2) - (capture_size / 2);

            let mut device: Option<ID3D11Device> = None;
            let mut context: Option<ID3D11DeviceContext> = None;
            
            D3D11CreateDevice(
                None,
                D3D_DRIVER_TYPE_HARDWARE,
                HMODULE::default(),
                D3D11_CREATE_DEVICE_FLAG(0),
                None,
                D3D11_SDK_VERSION,
                Some(&mut device),
                None,
                Some(&mut context),
            )?;

            let device = device.unwrap();
            let context = context.unwrap();

            let dxgi_device: IDXGIDevice = device.cast()?;
            let adapter = dxgi_device.GetAdapter()?;
            let output = adapter.EnumOutputs(0)?;
            let output1: IDXGIOutput1 = output.cast()?;

            let duplication = output1.DuplicateOutput(&device)?;

            let texture_desc = D3D11_TEXTURE2D_DESC {
                Width: capture_size,
                Height: capture_size,
                MipLevels: 1,
                ArraySize: 1,
                Format: DXGI_FORMAT_B8G8R8A8_UNORM,
                SampleDesc: DXGI_SAMPLE_DESC {
                    Count: 1,
                    Quality: 0,
                },
                Usage: D3D11_USAGE_STAGING,
                BindFlags: 0,
                CPUAccessFlags: D3D11_CPU_ACCESS_READ.0 as u32,
                MiscFlags: 0,
            };

            let mut staging_texture: Option<ID3D11Texture2D> = None;
            device.CreateTexture2D(&texture_desc, None, Some(&mut staging_texture))?;
            let staging_texture = staging_texture.unwrap();

            Ok(Self {
                context,
                duplication,
                staging_texture,
                center_x,
                center_y,
                capture_size,
            })
        }
    }

    fn capture_directx(&self) -> windows::core::Result<Option<Vec<egui::Color32>>> {
        unsafe {
            let mut frame_info = DXGI_OUTDUPL_FRAME_INFO::default();
            let mut desktop_resource: Option<IDXGIResource> = None;
            
            match self.duplication.AcquireNextFrame(0, &mut frame_info, &mut desktop_resource) {
                Ok(_) => {
                    if let Some(resource) = desktop_resource {
                        let desktop_texture: ID3D11Texture2D = resource.cast()?;

                        let src_box = D3D11_BOX {
                            left: self.center_x,
                            top: self.center_y,
                            front: 0,
                            right: self.center_x + self.capture_size,
                            bottom: self.center_y + self.capture_size,
                            back: 1,
                        };

                        self.context.CopySubresourceRegion(
                            &self.staging_texture,
                            0,
                            0,
                            0,
                            0,
                            &desktop_texture,
                            0,
                            Some(&src_box),
                        );

                        let mut mapped = D3D11_MAPPED_SUBRESOURCE::default();
                        self.context.Map(&self.staging_texture, 0, D3D11_MAP_READ, 0, Some(&mut mapped))?;

                        let mut pixels = Vec::with_capacity((self.capture_size * self.capture_size) as usize);
                        
                        let row_pitch = mapped.RowPitch as usize;
                        let bytes_per_pixel = 4;
                        let row_width_bytes = self.capture_size as usize * bytes_per_pixel;
                        
                        for row in 0..self.capture_size as usize {
                            let row_start = row * row_pitch;
                            let row_data = std::slice::from_raw_parts(
                                (mapped.pData as *const u8).add(row_start),
                                row_width_bytes,
                            );
                            
                            for chunk in row_data.chunks_exact(4) {
                                pixels.push(egui::Color32::from_rgba_premultiplied(
                                    chunk[2], // R
                                    chunk[1], // G
                                    chunk[0], // B
                                    chunk[3], // A
                                ));
                            }
                        }

                        self.context.Unmap(&self.staging_texture, 0);
                        let _ = self.duplication.ReleaseFrame();

                        Ok(Some(pixels))
                    } else {
                        Err(windows::core::Error::from(E_FAIL))
                    }
                }
                Err(e) => {
                    if e.code() == DXGI_ERROR_WAIT_TIMEOUT {
                        Ok(None)
                    } else {
                        Err(e)
                    }
                }
            }
        }
    }
}

struct ScreenCaptureApp {
    texture: Option<TextureHandle>,
    filtered_texture: Option<TextureHandle>,
    frame_data: Arc<Mutex<Option<FrameData>>>,
    current_fps: f32,
    last_texture_update: Instant,
    texture_update_interval: Duration,
    show_filtered: bool,
    config: Arc<Mutex<AppConfig>>,
    aim_assist: Arc<Mutex<AimAssist>>,
    current_capture_size: u32,
    is_running: Arc<AtomicBool>,
    show_preview: bool,
}

impl ScreenCaptureApp {
    fn new(_cc: &eframe::CreationContext<'_>) -> Self {
        let frame_data = Arc::new(Mutex::new(None));
        let frame_data_clone = frame_data.clone();
        
        let config = Arc::new(Mutex::new(AppConfig::load_from_file()));
        let config_clone = config.clone();
        
        let aim_assist = Arc::new(Mutex::new(AimAssist::new(config.clone())));
        let aim_assist_clone = aim_assist.clone();

        let is_running = Arc::new(AtomicBool::new(true));
        let is_running_clone = is_running.clone();

        // Start optimized DirectX capture thread
        thread::spawn(move || {
            unsafe {
                let handle = GetCurrentThread();
                let _ = SetThreadPriority(handle, THREAD_PRIORITY_TIME_CRITICAL);
            }
            
            capture_loop_optimized(frame_data_clone, config_clone, aim_assist_clone, is_running_clone);
        });

        let initial_capture_size = config.lock().aim.aim_fov;
        
        Self {
            texture: None,
            filtered_texture: None,
            frame_data,
            current_fps: 0.0,
            last_texture_update: Instant::now(),
            texture_update_interval: Duration::from_millis(16),
            show_filtered: true,
            config,
            aim_assist,
            current_capture_size: initial_capture_size,
            is_running,
            show_preview: true,
        }
    }
}

impl eframe::App for ScreenCaptureApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Handle frame data updates only when preview is enabled
        if self.show_preview {
            if let Some(data) = self.frame_data.lock().take() {
                self.current_fps = data.fps;

                if self.last_texture_update.elapsed() >= self.texture_update_interval {
                    let config = self.config.lock();
                    if config.aim.aim_fov != self.current_capture_size {
                        self.current_capture_size = config.aim.aim_fov;
                    }
                    drop(config);
                    
                    let color_image = egui::ColorImage {
                        size: [self.current_capture_size as usize, self.current_capture_size as usize],
                        pixels: data.pixels,
                    };

                    let filtered_color_image = egui::ColorImage {
                        size: [self.current_capture_size as usize, self.current_capture_size as usize],
                        pixels: data.filtered_pixels,
                    };

                    if let Some(ref mut texture) = self.texture {
                        texture.set(color_image, egui::TextureOptions::NEAREST);
                    } else {
                        self.texture = Some(ctx.load_texture(
                            "screen_capture",
                            color_image,
                            egui::TextureOptions::NEAREST,
                        ));
                    }

                    if let Some(ref mut filtered_texture) = self.filtered_texture {
                        filtered_texture.set(filtered_color_image, egui::TextureOptions::NEAREST);
                    } else {
                        self.filtered_texture = Some(ctx.load_texture(
                            "screen_capture_filtered",
                            filtered_color_image,
                            egui::TextureOptions::NEAREST,
                        ));
                    }

                    self.last_texture_update = Instant::now();
                }
            }
        }

        // Handle keyboard shortcuts
        ctx.input(|i| {
            if i.key_pressed(egui::Key::F7) {
                let mut config = self.config.lock();
                config.trigger_bot = !config.trigger_bot;
            }
            
            if i.key_pressed(egui::Key::F8) {
                let mut config = self.config.lock();
                config.aim_assist = !config.aim_assist;
            }

            if i.key_pressed(egui::Key::F9) {
                self.show_preview = !self.show_preview;
            }
        });

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("⚡ High-Performance Screen Capture");
            
            ui.horizontal(|ui| {
                ui.colored_label(egui::Color32::GREEN, format!("FPS: {:.0}", self.current_fps));
                
                // Preview toggle button with better visual feedback
                let preview_button_text = if self.show_preview { 
                    "⏸️ Hide Preview" 
                } else { 
                    "▶️ Show Preview" 
                };
                let preview_button_color = if self.show_preview { 
                    egui::Color32::YELLOW 
                } else { 
                    egui::Color32::GREEN 
                };
                
                if ui.add(egui::Button::new(preview_button_text).fill(preview_button_color)).clicked() {
                    self.show_preview = !self.show_preview;
                }
                
                if ui.button("🛑 Stop All").clicked() {
                    self.is_running.store(false, Ordering::Relaxed);
                    std::process::exit(0);
                }
            });

            // Show status information
            ui.horizontal(|ui| {
                let config = self.config.lock();
                if config.aim_assist {
                    ui.colored_label(egui::Color32::GREEN, "🎯 AIM: ACTIVE");
                } else {
                    ui.colored_label(egui::Color32::GRAY, "🎯 AIM: OFF");
                }
                
                if config.trigger_bot {
                    ui.colored_label(egui::Color32::RED, "🔫 TRIGGER: ARMED");
                } else {
                    ui.colored_label(egui::Color32::GRAY, "🔫 TRIGGER: SAFE");
                }
                
                if let Some(aim_assist_guard) = self.aim_assist.try_lock() {
                    if aim_assist_guard.arduino.is_some() {
                        ui.colored_label(egui::Color32::GREEN, "🎮 ARDUINO: CONNECTED");
                    } else {
                        ui.colored_label(egui::Color32::RED, "🎮 ARDUINO: DISCONNECTED");
                    }
                }
            });

            // Controls
            ui.horizontal(|ui| {
                let mut config = self.config.lock();
                ui.checkbox(&mut config.aim_assist, "🎯 Aim Assist");
                ui.checkbox(&mut config.trigger_bot, "🔫 Trigger Bot");
                
                // Add preview checkbox for easier access
                ui.separator();
                ui.checkbox(&mut self.show_preview, "📺 Show Preview");
            });
            
            // Configuration sliders
            ui.collapsing("⚙️ Settings", |ui| {
                let mut config = self.config.lock();
                
                // Color preset selection
                ui.horizontal(|ui| {
                    ui.label("Color Preset:");
                    egui::ComboBox::from_label("")
                        .selected_text(config.color_preset.name())
                        .show_ui(ui, |ui| {
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Yellow, "Yellow");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Yellow2, "Yellow 2");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Purple, "Purple");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::AntiAstra, "Anti Astra");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Red, "Red");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Custom, "Custom");
                        });
                });
                
                // Custom HSV range controls (only show if Custom is selected)
                if config.color_preset == ColorPreset::Custom {
                    ui.separator();
                    ui.label("Custom HSV Range:");
                    
                    ui.horizontal(|ui| {
                        ui.label("H Min:");
                        ui.add(egui::Slider::new(&mut config.custom_hsv_lower[0], 0.0..=1.0).step_by(0.01));
                        ui.label("H Max:");
                        ui.add(egui::Slider::new(&mut config.custom_hsv_upper[0], 0.0..=1.0).step_by(0.01));
                    });
                    
                    ui.horizontal(|ui| {
                        ui.label("S Min:");
                        ui.add(egui::Slider::new(&mut config.custom_hsv_lower[1], 0.0..=1.0).step_by(0.01));
                        ui.label("S Max:");
                        ui.add(egui::Slider::new(&mut config.custom_hsv_upper[1], 0.0..=1.0).step_by(0.01));
                    });
                    
                    ui.horizontal(|ui| {
                        ui.label("V Min:");
                        ui.add(egui::Slider::new(&mut config.custom_hsv_lower[2], 0.0..=1.0).step_by(0.01));
                        ui.label("V Max:");
                        ui.add(egui::Slider::new(&mut config.custom_hsv_upper[2], 0.0..=1.0).step_by(0.01));
                    });
                }
                
                ui.separator();
                
                ui.horizontal(|ui| {
                    ui.label("Aim FOV:");
                    ui.add(egui::Slider::new(&mut config.aim.aim_fov, 10..=200).suffix("px"));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim Speed X:");
                    ui.add(egui::Slider::new(&mut config.aim.aim_speed_x, 0.1..=2.0).step_by(0.1));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim Speed Y:");
                    ui.add(egui::Slider::new(&mut config.aim.aim_speed_y, 0.1..=2.0).step_by(0.1));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Trigger FOV:");
                    ui.add(egui::Slider::new(&mut config.trigger.trigger_fov, 5..=50).suffix("px"));
                });
            });
            
            ui.separator();
            
            // Preview section (only when enabled)
            if self.show_preview {
                ui.checkbox(&mut self.show_filtered, "🎨 Show Color Filter");
                
                let texture_to_show = if self.show_filtered {
                    &self.filtered_texture
                } else {
                    &self.texture
                };
                
                if let Some(texture) = texture_to_show {
                    let scale_factor = 3.0;
                    let display_size = egui::Vec2::new(
                        self.current_capture_size as f32 * scale_factor,
                        self.current_capture_size as f32 * scale_factor,
                    );
                    
                    ui.add(egui::Image::new(texture).fit_to_exact_size(display_size));
                    
                    ui.label(format!("{}x{} → {}x{} (3x scale)", 
                        self.current_capture_size, self.current_capture_size,
                        self.current_capture_size * 3, self.current_capture_size * 3));
                } else {
                    ui.label("Preview disabled for better performance");
                }
            } else {
                ui.colored_label(egui::Color32::YELLOW, "📺 Preview stopped for maximum performance");
                ui.label("Aim assist and trigger bot still running in background");
            }
            
            ui.separator();
            ui.horizontal(|ui| {
                ui.colored_label(egui::Color32::YELLOW, "🎮 F7: Toggle Trigger | F8: Toggle Aim | F9: Toggle Preview");
            });
        });

        if self.show_preview {
            ctx.request_repaint_after(Duration::from_millis(16));
        } else {
            ctx.request_repaint_after(Duration::from_millis(100));
        }
    }
}

fn capture_loop_optimized(
    frame_data: Arc<Mutex<Option<FrameData>>>,
    config: Arc<Mutex<AppConfig>>,
    aim_assist: Arc<Mutex<AimAssist>>,
    is_running: Arc<AtomicBool>,
) {
    let mut current_capture_size = config.lock().aim.aim_fov;
    let mut capturer = match DirectXCapture::new(current_capture_size) {
        Ok(c) => c,
        Err(_) => return,
    };

    let mut fps_counter = 0u32;
    let mut fps_timer = Instant::now();
    let mut current_fps = 0.0f32;
    let mut last_valid_pixels: Option<Vec<egui::Color32>> = None;

    while is_running.load(Ordering::Relaxed) {
        // Check for capture size changes
        let new_capture_size = config.lock().aim.aim_fov;
        if new_capture_size != current_capture_size {
            current_capture_size = new_capture_size;
            capturer = match DirectXCapture::new(current_capture_size) {
                Ok(c) => c,
                Err(_) => continue,
            };
        }

        match capturer.capture_directx() {
            Ok(Some(pixels)) => {
                fps_counter += 1;
                if fps_timer.elapsed() >= Duration::from_secs(1) {
                    current_fps = fps_counter as f32;
                    fps_counter = 0;
                    fps_timer = Instant::now();
                }

                last_valid_pixels = Some(pixels.clone());

                // Process aim assist and trigger bot in parallel
                if let Some(mut aim_assist_guard) = aim_assist.try_lock() {
                    let current_config = config.lock();
                    let capture_size = current_config.aim.aim_fov;
                    drop(current_config);
                    
                    let _ = aim_assist_guard.process_aim_assist(&pixels, capture_size);
                    let _ = aim_assist_guard.process_trigger_bot(&pixels, capture_size);
                }

                // Apply color filtering in parallel
                let current_config = config.lock();
                let (hsv_lower, hsv_upper) = if current_config.color_preset == ColorPreset::Custom {
                    (current_config.custom_hsv_lower, current_config.custom_hsv_upper)
                } else {
                    current_config.color_preset.to_hsv_range()
                };
                drop(current_config);
                
                let filtered_pixels = apply_color_filter(&pixels, hsv_lower, hsv_upper);

                let data = FrameData {
                    pixels,
                    filtered_pixels,
                    fps: current_fps,
                };

                *frame_data.lock() = Some(data);
            }
            Ok(None) => {
                // No new frame - continue without updating
            }
            Err(_) => {
                // Error - use backup if available
                if let Some(ref backup_pixels) = last_valid_pixels {
                    let current_config = config.lock();
                    let (hsv_lower, hsv_upper) = if current_config.color_preset == ColorPreset::Custom {
                        (current_config.custom_hsv_lower, current_config.custom_hsv_upper)
                    } else {
                        current_config.color_preset.to_hsv_range()
                    };
                    drop(current_config);
                    
                    let filtered_pixels = apply_color_filter(backup_pixels, hsv_lower, hsv_upper);
                    let data = FrameData {
                        pixels: backup_pixels.clone(),
                        filtered_pixels,
                        fps: current_fps,
                    };
                    *frame_data.lock() = Some(data);
                }
                
                thread::sleep(Duration::from_millis(1));
                continue;
            }
        }

        // Minimal sleep for high performance
        thread::sleep(Duration::from_micros(100));
    }
}

impl AimAssist {
    fn new(config: Arc<Mutex<AppConfig>>) -> Self {
        let arduino = ArduinoController::new().ok();
        
        Self {
            config,
            arduino,
            last_aim_time: Instant::now(),
            last_trigger_time: Instant::now(),
        }
    }
    
    fn detect_target_in_pixels(&self, pixels: &[egui::Color32], fov: u32, hsv_lower: [f32; 3], hsv_upper: [f32; 3]) -> Option<TargetInfo> {
        let mut total_weight = 0.0;
        let mut weighted_x = 0.0;
        let mut weighted_y = 0.0;
        let mut target_count = 0;
        
        for (i, &pixel) in pixels.iter().enumerate() {
            let x = (i % fov as usize) as f32;
            let y = (i / fov as usize) as f32;
            
            let r = pixel.r() as f32 / 255.0;
            let g = pixel.g() as f32 / 255.0;
            let b = pixel.b() as f32 / 255.0;
            
            let hsv = rgb_to_hsv(r, g, b);
            
            if is_target_color(hsv, hsv_lower, hsv_upper) {
                target_count += 1;
                let weight = (r + g + b) / 3.0;
                total_weight += weight;
                weighted_x += x * weight;
                weighted_y += y * weight;
            }
        }
        
        if total_weight > 0.0 && target_count > 5 {
            let center_x = weighted_x / total_weight;
            let center_y = weighted_y / total_weight;
            let confidence = (target_count as f32 / (fov * fov) as f32).min(1.0);
            
            Some(TargetInfo {
                center_x,
                center_y,
                confidence,
            })
        } else {
            None
        }
    }
    
    fn process_aim_assist(&mut self, pixels: &[egui::Color32], capture_size: u32) -> Result<()> {
        let config = self.config.lock().clone();
        
        if !config.aim_assist {
            return Ok(());
        }
        
        let aim_sleep = Duration::from_secs_f32(1.0 / config.aim.aim_fps as f32);
        if self.last_aim_time.elapsed() < aim_sleep {
            return Ok(());
        }
        
        let should_aim = config.aim_key == "auto" || unsafe {
            if let Ok(key_code) = u32::from_str_radix(&config.aim_key.trim_start_matches("0x"), 16) {
                GetAsyncKeyState(key_code as i32) < 0
            } else {
                false
            }
        };
        
        if !should_aim {
            return Ok(());
        }
        
        // Get color range
        let (hsv_lower, hsv_upper) = if config.color_preset == ColorPreset::Custom {
            (config.custom_hsv_lower, config.custom_hsv_upper)
        } else {
            config.color_preset.to_hsv_range()
        };
        
        if let Some(target) = self.detect_target_in_pixels(pixels, capture_size, hsv_lower, hsv_upper) {
            let fov_center = capture_size as f32 / 2.0;
            let x_offset = target.center_x - fov_center;
            let y_offset = target.center_y - fov_center;
            
            let adjusted_y = y_offset - config.aim.aim_offset as f32;
            let adjusted_x = x_offset - config.aim.aim_offset_x as f32;
            
            let x_move = (adjusted_x * config.aim.aim_speed_x) as i32;
            let y_move = (adjusted_y * config.aim.aim_speed_y) as i32;
            
            if let Some(ref mut arduino) = self.arduino {
                arduino.mousemove_aim(x_move, y_move, "movemouse")?;
            }
        }
        
        self.last_aim_time = Instant::now();
        Ok(())
    }
    
    fn process_trigger_bot(&mut self, pixels: &[egui::Color32], capture_size: u32) -> Result<()> {
        let config = self.config.lock().clone();
        
        if !config.trigger_bot {
            return Ok(());
        }
        
        let trigger_sleep = Duration::from_secs_f32(1.0 / config.trigger.trigger_fps as f32);
        if self.last_trigger_time.elapsed() < trigger_sleep {
            return Ok(());
        }
        
        let trigger_pressed = unsafe {
            GetAsyncKeyState(config.trigger_key as i32) < 0
        };
        
        if !trigger_pressed {
            return Ok(());
        }
        
        let trigger_fov = config.trigger.trigger_fov.min(capture_size);
        let start_offset = (capture_size - trigger_fov) / 2;
        
        let mut trigger_pixels = Vec::new();
        for y in start_offset..(start_offset + trigger_fov) {
            for x in start_offset..(start_offset + trigger_fov) {
                let idx = (y * capture_size + x) as usize;
                if idx < pixels.len() {
                    trigger_pixels.push(pixels[idx]);
                }
            }
        }
        
        // Get color range
        let (hsv_lower, hsv_upper) = if config.color_preset == ColorPreset::Custom {
            (config.custom_hsv_lower, config.custom_hsv_upper)
        } else {
            config.color_preset.to_hsv_range()
        };
        
        if let Some(_target) = self.detect_target_in_pixels(&trigger_pixels, trigger_fov, hsv_lower, hsv_upper) {
            std::thread::sleep(Duration::from_secs_f32(config.trigger.trigger_delay));
            
            if let Some(ref mut arduino) = self.arduino {
                arduino.mouse_click()?;
            }
        }
        
        self.last_trigger_time = Instant::now();
        Ok(())
    }
}

fn main() -> eframe::Result<()> {
    unsafe {
        let process = GetCurrentProcess();
        let _ = SetPriorityClass(process, HIGH_PRIORITY_CLASS);
    }

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([480.0, 450.0])
            .with_title("⚡ High-Performance Screen Capture")
            .with_resizable(true),
        vsync: false,
        ..Default::default()
    };
    
    eframe::run_native(
        "High-Performance Screen Capture",
        options,
        Box::new(|cc| Ok(Box::new(ScreenCaptureApp::new(cc)))),
    )
}
