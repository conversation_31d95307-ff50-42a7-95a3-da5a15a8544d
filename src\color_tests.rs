#[cfg(test)]
mod tests {
    use crate::{ColorPreset, CaptureConfig};
    
    #[test]
    fn test_opencv_to_rust_hsv_conversion() {
        // Test Purple color conversion (main.py: [144, 72, 150] - [152, 255, 255])
        let (lower, upper) = ColorPreset::Purple.to_hsv_range();
        
        // Expected conversions:
        // H: 144/179 ≈ 0.804, 152/179 ≈ 0.849
        // S: 72/255 ≈ 0.282, 255/255 = 1.0
        // V: 150/255 ≈ 0.588, 255/255 = 1.0
        
        assert!((lower[0] - 0.804).abs() < 0.01, "Lower H conversion failed");
        assert!((lower[1] - 0.282).abs() < 0.01, "Lower S conversion failed");
        assert!((lower[2] - 0.588).abs() < 0.01, "Lower V conversion failed");
        
        assert!((upper[0] - 0.849).abs() < 0.01, "Upper H conversion failed");
        assert!((upper[1] - 1.0).abs() < 0.01, "Upper S conversion failed");
        assert!((upper[2] - 1.0).abs() < 0.01, "Upper V conversion failed");
    }
    
    #[test]
    fn test_yellow_color_conversion() {
        // Test Yellow color conversion (main.py: [30, 125, 150] - [30, 255, 255])
        let (lower, upper) = ColorPreset::Yellow.to_hsv_range();
        
        // Expected conversions:
        // H: 30/179 ≈ 0.168, 30/179 ≈ 0.168
        // S: 125/255 ≈ 0.490, 255/255 = 1.0
        // V: 150/255 ≈ 0.588, 255/255 = 1.0
        
        assert!((lower[0] - 0.168).abs() < 0.01, "Yellow Lower H conversion failed");
        assert!((lower[1] - 0.490).abs() < 0.01, "Yellow Lower S conversion failed");
        assert!((lower[2] - 0.588).abs() < 0.01, "Yellow Lower V conversion failed");
        
        assert!((upper[0] - 0.168).abs() < 0.01, "Yellow Upper H conversion failed");
        assert!((upper[1] - 1.0).abs() < 0.01, "Yellow Upper S conversion failed");
        assert!((upper[2] - 1.0).abs() < 0.01, "Yellow Upper V conversion failed");
    }
    
    #[test]
    fn test_red_color_conversion() {
        // Test Red color conversion (main.py: [0, 170, 150] - [5, 255, 255])
        let (lower, upper) = ColorPreset::Red.to_hsv_range();
        
        // Expected conversions:
        // H: 0/179 = 0.0, 5/179 ≈ 0.028
        // S: 170/255 ≈ 0.667, 255/255 = 1.0
        // V: 150/255 ≈ 0.588, 255/255 = 1.0
        
        assert!((lower[0] - 0.0).abs() < 0.01, "Red Lower H conversion failed");
        assert!((lower[1] - 0.667).abs() < 0.01, "Red Lower S conversion failed");
        assert!((lower[2] - 0.588).abs() < 0.01, "Red Lower V conversion failed");
        
        assert!((upper[0] - 0.028).abs() < 0.01, "Red Upper H conversion failed");
        assert!((upper[1] - 1.0).abs() < 0.01, "Red Upper S conversion failed");
        assert!((upper[2] - 1.0).abs() < 0.01, "Red Upper V conversion failed");
    }
    
    #[test]
    fn test_config_default_values() {
        let config = CaptureConfig::default();
        
        assert_eq!(config.color_preset, ColorPreset::Purple);
        assert_eq!(config.custom_hsv_lower, [0.8, 0.282, 0.588]);
        assert_eq!(config.custom_hsv_upper, [0.844, 1.0, 1.0]);
    }
    
    #[test]
    fn test_custom_color_range() {
        let mut config = CaptureConfig::default();
        config.color_preset = ColorPreset::Custom;
        config.custom_hsv_lower = [0.1, 0.2, 0.3];
        config.custom_hsv_upper = [0.4, 0.5, 0.6];
        
        let (lower, upper) = config.get_current_hsv_range();
        
        assert_eq!(lower, [0.1, 0.2, 0.3]);
        assert_eq!(upper, [0.4, 0.5, 0.6]);
    }
}
